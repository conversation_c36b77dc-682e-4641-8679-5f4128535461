<?php

namespace SouthSafari\Core;

/**
 * South Safari Application Core
 * 
 * Main application class that handles initialization, routing, and request processing
 */
class App
{
    private static $instance = null;
    private $router;
    private $database;
    private $session;
    private $config;
    private $request;
    private $response;

    /**
     * Private constructor for singleton pattern
     */
    private function __construct()
    {
        $this->initializeApplication();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the application
     */
    private function initializeApplication()
    {
        // Set error reporting based on debug mode
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
        }

        // Set timezone
        if (defined('APP_TIMEZONE')) {
            date_default_timezone_set(APP_TIMEZONE);
        }

        // Initialize core components
        $this->initializeConfig();
        $this->initializeDatabase();
        $this->initializeSession();
        $this->initializeRouter();
        $this->initializeRequest();
        $this->initializeResponse();

        // Set security headers
        $this->setSecurityHeaders();

        // Register error handlers
        $this->registerErrorHandlers();
    }

    /**
     * Initialize configuration
     */
    private function initializeConfig()
    {
        $this->config = new Config();
    }

    /**
     * Initialize database connection
     */
    private function initializeDatabase()
    {
        $this->database = new Database();
    }

    /**
     * Initialize session management
     */
    private function initializeSession()
    {
        $this->session = new Session();
    }

    /**
     * Initialize router
     */
    private function initializeRouter()
    {
        $this->router = new Router();
        $this->loadRoutes();
    }

    /**
     * Initialize request object
     */
    private function initializeRequest()
    {
        $this->request = new Request();
    }

    /**
     * Initialize response object
     */
    private function initializeResponse()
    {
        $this->response = new Response();
    }

    /**
     * Load application routes
     */
    private function loadRoutes()
    {
        // Public routes
        $this->router->get('/', 'HomeController@index');
        $this->router->get('/projects', 'ProjectController@index');
        $this->router->get('/projects/{slug}', 'ProjectController@show');
        $this->router->get('/categories', 'CategoryController@index');
        $this->router->get('/categories/{slug}', 'CategoryController@show');
        $this->router->get('/apply/{project_id}', 'ApplicationController@create');
        $this->router->post('/apply/{project_id}', 'ApplicationController@store');
        $this->router->get('/contact', 'ContactController@index');
        $this->router->post('/contact', 'ContactController@send');

        // Authentication routes
        $this->router->get('/login', 'AuthController@showLogin');
        $this->router->post('/login', 'AuthController@login');
        $this->router->get('/register', 'AuthController@showRegister');
        $this->router->post('/register', 'AuthController@register');
        $this->router->post('/logout', 'AuthController@logout');
        $this->router->get('/forgot-password', 'AuthController@showForgotPassword');
        $this->router->post('/forgot-password', 'AuthController@forgotPassword');
        $this->router->get('/reset-password/{token}', 'AuthController@showResetPassword');
        $this->router->post('/reset-password', 'AuthController@resetPassword');

        // Developer dashboard routes (protected)
        $this->router->group(['prefix' => '/dashboard', 'middleware' => 'auth'], function($router) {
            $router->get('/', 'DashboardController@index');
            $router->get('/profile', 'DashboardController@profile');
            $router->post('/profile', 'DashboardController@updateProfile');
            $router->get('/applications', 'DashboardController@applications');
            $router->get('/messages', 'MessageController@index');
            $router->get('/messages/{thread_id}', 'MessageController@show');
            $router->post('/messages', 'MessageController@send');
        });

        // Admin routes (protected)
        $this->router->group(['prefix' => '/admin', 'middleware' => 'admin'], function($router) {
            $router->get('/', 'Admin\DashboardController@index');
            
            // Project management
            $router->get('/projects', 'Admin\ProjectController@index');
            $router->get('/projects/create', 'Admin\ProjectController@create');
            $router->post('/projects', 'Admin\ProjectController@store');
            $router->get('/projects/{id}/edit', 'Admin\ProjectController@edit');
            $router->put('/projects/{id}', 'Admin\ProjectController@update');
            $router->delete('/projects/{id}', 'Admin\ProjectController@destroy');
            
            // Application management
            $router->get('/applications', 'Admin\ApplicationController@index');
            $router->get('/applications/{id}', 'Admin\ApplicationController@show');
            $router->put('/applications/{id}/status', 'Admin\ApplicationController@updateStatus');
            
            // Message management
            $router->get('/messages', 'Admin\MessageController@index');
            $router->get('/messages/{thread_id}', 'Admin\MessageController@show');
            $router->post('/messages', 'Admin\MessageController@send');
            
            // User management
            $router->get('/users', 'Admin\UserController@index');
            $router->get('/users/{id}', 'Admin\UserController@show');
            $router->put('/users/{id}/status', 'Admin\UserController@updateStatus');
            
            // Partnership management
            $router->get('/partnerships', 'Admin\PartnershipController@index');
            $router->get('/partnerships/create', 'Admin\PartnershipController@create');
            $router->post('/partnerships', 'Admin\PartnershipController@store');
            $router->get('/partnerships/{id}', 'Admin\PartnershipController@show');
            $router->put('/partnerships/{id}', 'Admin\PartnershipController@update');
            
            // Settings
            $router->get('/settings', 'Admin\SettingsController@index');
            $router->post('/settings', 'Admin\SettingsController@update');
        });

        // API routes
        $this->router->group(['prefix' => '/api/v1'], function($router) {
            $router->get('/projects', 'Api\ProjectController@index');
            $router->get('/projects/{id}', 'Api\ProjectController@show');
            $router->post('/applications', 'Api\ApplicationController@store');
            $router->get('/categories', 'Api\CategoryController@index');
        });

        // File upload routes
        $this->router->post('/upload', 'FileController@upload');
        $this->get('/uploads/{filename}', 'FileController@serve');
    }

    /**
     * Set security headers
     */
    private function setSecurityHeaders()
    {
        if (defined('SECURITY_HEADERS')) {
            foreach (SECURITY_HEADERS as $header => $value) {
                header("$header: $value");
            }
        }
    }

    /**
     * Register error handlers
     */
    private function registerErrorHandlers()
    {
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
    }

    /**
     * Handle PHP errors
     */
    public function handleError($severity, $message, $file, $line)
    {
        if (!(error_reporting() & $severity)) {
            return false;
        }

        $error = [
            'type' => 'PHP Error',
            'severity' => $severity,
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->logError($error);

        if (DEBUG_MODE) {
            $this->displayError($error);
        } else {
            $this->response->error500();
        }
    }

    /**
     * Handle uncaught exceptions
     */
    public function handleException($exception)
    {
        $error = [
            'type' => 'Uncaught Exception',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->logError($error);

        if (DEBUG_MODE) {
            $this->displayError($error);
        } else {
            $this->response->error500();
        }
    }

    /**
     * Handle fatal errors
     */
    public function handleShutdown()
    {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $this->handleError($error['type'], $error['message'], $error['file'], $error['line']);
        }
    }

    /**
     * Log error to file
     */
    private function logError($error)
    {
        if (defined('LOG_ERRORS') && LOG_ERRORS) {
            $logFile = LOG_PATH . 'error.log';
            $logEntry = date('Y-m-d H:i:s') . ' - ' . $error['type'] . ': ' . $error['message'] . 
                       ' in ' . $error['file'] . ' on line ' . $error['line'] . PHP_EOL;
            
            if (!is_dir(LOG_PATH)) {
                mkdir(LOG_PATH, 0755, true);
            }
            
            file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        }
    }

    /**
     * Display error for debugging
     */
    private function displayError($error)
    {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<h4>{$error['type']}</h4>";
        echo "<p><strong>Message:</strong> {$error['message']}</p>";
        echo "<p><strong>File:</strong> {$error['file']}</p>";
        echo "<p><strong>Line:</strong> {$error['line']}</p>";
        if (isset($error['trace'])) {
            echo "<details><summary>Stack Trace</summary><pre>{$error['trace']}</pre></details>";
        }
        echo "</div>";
    }

    /**
     * Run the application
     */
    public function run()
    {
        try {
            // Handle the request
            $this->router->dispatch($this->request, $this->response);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Get application components
     */
    public function getDatabase() { return $this->database; }
    public function getSession() { return $this->session; }
    public function getRouter() { return $this->router; }
    public function getRequest() { return $this->request; }
    public function getResponse() { return $this->response; }
    public function getConfig() { return $this->config; }
}

?>
