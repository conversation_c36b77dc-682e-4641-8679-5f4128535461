<?php

namespace SouthSafari\Core;

/**
 * Router Class
 * 
 * Handles URL routing and request dispatching
 */
class Router
{
    private $routes = [];
    private $middlewares = [];
    private $currentGroup = [];

    /**
     * Add GET route
     */
    public function get($uri, $action)
    {
        $this->addRoute('GET', $uri, $action);
        return $this;
    }

    /**
     * Add POST route
     */
    public function post($uri, $action)
    {
        $this->addRoute('POST', $uri, $action);
        return $this;
    }

    /**
     * Add PUT route
     */
    public function put($uri, $action)
    {
        $this->addRoute('PUT', $uri, $action);
        return $this;
    }

    /**
     * Add DELETE route
     */
    public function delete($uri, $action)
    {
        $this->addRoute('DELETE', $uri, $action);
        return $this;
    }

    /**
     * Add PATCH route
     */
    public function patch($uri, $action)
    {
        $this->addRoute('PATCH', $uri, $action);
        return $this;
    }

    /**
     * Add route for any HTTP method
     */
    public function any($uri, $action)
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        foreach ($methods as $method) {
            $this->addRoute($method, $uri, $action);
        }
        return $this;
    }

    /**
     * Create route group with shared attributes
     */
    public function group($attributes, $callback)
    {
        $previousGroup = $this->currentGroup;
        $this->currentGroup = array_merge($this->currentGroup, $attributes);
        
        $callback($this);
        
        $this->currentGroup = $previousGroup;
    }

    /**
     * Add route to routes array
     */
    private function addRoute($method, $uri, $action)
    {
        // Apply group prefix if exists
        if (isset($this->currentGroup['prefix'])) {
            $uri = rtrim($this->currentGroup['prefix'], '/') . '/' . ltrim($uri, '/');
        }

        // Apply group middleware if exists
        $middleware = [];
        if (isset($this->currentGroup['middleware'])) {
            $middleware = is_array($this->currentGroup['middleware']) 
                ? $this->currentGroup['middleware'] 
                : [$this->currentGroup['middleware']];
        }

        $this->routes[] = [
            'method' => $method,
            'uri' => $uri,
            'action' => $action,
            'middleware' => $middleware
        ];
    }

    /**
     * Dispatch the request to appropriate controller
     */
    public function dispatch($request, $response)
    {
        $method = $request->getMethod();
        $uri = $request->getUri();

        // Find matching route
        $route = $this->findRoute($method, $uri);

        if (!$route) {
            $response->error404();
            return;
        }

        // Extract parameters from URI
        $params = $this->extractParams($route['uri'], $uri);

        // Run middleware
        if (!$this->runMiddleware($route['middleware'], $request, $response)) {
            return;
        }

        // Execute controller action
        $this->executeAction($route['action'], $params, $request, $response);
    }

    /**
     * Find matching route
     */
    private function findRoute($method, $uri)
    {
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchUri($route['uri'], $uri)) {
                return $route;
            }
        }
        return null;
    }

    /**
     * Check if URI matches route pattern
     */
    private function matchUri($routeUri, $requestUri)
    {
        // Convert route parameters to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routeUri);
        $pattern = '#^' . $pattern . '$#';

        return preg_match($pattern, $requestUri);
    }

    /**
     * Extract parameters from URI
     */
    private function extractParams($routeUri, $requestUri)
    {
        $params = [];
        
        // Get parameter names from route
        preg_match_all('/\{([^}]+)\}/', $routeUri, $paramNames);
        
        // Get parameter values from request URI
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routeUri);
        $pattern = '#^' . $pattern . '$#';
        
        if (preg_match($pattern, $requestUri, $matches)) {
            array_shift($matches); // Remove full match
            
            foreach ($paramNames[1] as $index => $name) {
                if (isset($matches[$index])) {
                    $params[$name] = $matches[$index];
                }
            }
        }
        
        return $params;
    }

    /**
     * Run middleware for the route
     */
    private function runMiddleware($middlewares, $request, $response)
    {
        foreach ($middlewares as $middleware) {
            $middlewareClass = $this->resolveMiddleware($middleware);
            
            if (!$middlewareClass) {
                continue;
            }

            $middlewareInstance = new $middlewareClass();
            
            if (!$middlewareInstance->handle($request, $response)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Resolve middleware class name
     */
    private function resolveMiddleware($middleware)
    {
        $middlewareMap = [
            'auth' => 'SouthSafari\\Middleware\\AuthMiddleware',
            'admin' => 'SouthSafari\\Middleware\\AdminMiddleware',
            'guest' => 'SouthSafari\\Middleware\\GuestMiddleware',
            'csrf' => 'SouthSafari\\Middleware\\CsrfMiddleware',
            'throttle' => 'SouthSafari\\Middleware\\ThrottleMiddleware'
        ];

        if (isset($middlewareMap[$middleware])) {
            return $middlewareMap[$middleware];
        }

        // If it's already a full class name
        if (class_exists($middleware)) {
            return $middleware;
        }

        return null;
    }

    /**
     * Execute controller action
     */
    private function executeAction($action, $params, $request, $response)
    {
        if (is_callable($action)) {
            // If action is a closure
            call_user_func_array($action, [$request, $response, $params]);
            return;
        }

        if (is_string($action)) {
            // Parse controller@method format
            list($controller, $method) = explode('@', $action);
            
            // Resolve controller class
            $controllerClass = $this->resolveController($controller);
            
            if (!class_exists($controllerClass)) {
                throw new Exception("Controller {$controllerClass} not found");
            }

            $controllerInstance = new $controllerClass();
            
            if (!method_exists($controllerInstance, $method)) {
                throw new Exception("Method {$method} not found in {$controllerClass}");
            }

            // Call controller method
            call_user_func_array([$controllerInstance, $method], [$request, $response, $params]);
            return;
        }

        throw new Exception("Invalid route action");
    }

    /**
     * Resolve controller class name
     */
    private function resolveController($controller)
    {
        // If it's already a full class name
        if (class_exists($controller)) {
            return $controller;
        }

        // Check if it's an admin controller
        if (strpos($controller, 'Admin\\') === 0) {
            return 'SouthSafari\\Controllers\\' . $controller;
        }

        // Check if it's an API controller
        if (strpos($controller, 'Api\\') === 0) {
            return 'SouthSafari\\Controllers\\' . $controller;
        }

        // Default controller namespace
        return 'SouthSafari\\Controllers\\' . $controller;
    }

    /**
     * Generate URL for named route
     */
    public function url($name, $params = [])
    {
        // This would be implemented if we had named routes
        // For now, we'll just build URLs manually
        $url = BASE_URL;
        
        if (is_array($params) && !empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }

    /**
     * Get all registered routes
     */
    public function getRoutes()
    {
        return $this->routes;
    }

    /**
     * Register middleware
     */
    public function middleware($name, $class)
    {
        $this->middlewares[$name] = $class;
    }

    /**
     * Check if route exists
     */
    public function hasRoute($method, $uri)
    {
        return $this->findRoute($method, $uri) !== null;
    }
}

?>
